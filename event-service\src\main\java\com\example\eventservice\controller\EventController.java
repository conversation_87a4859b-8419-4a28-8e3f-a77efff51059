package com.example.eventservice.controller;

import com.example.eventservice.entity.Event;
import com.example.eventservice.service.EventService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller REST pour la gestion des événements
 * 
 * Endpoints :
 * - POST /events – Créer événement
 * - GET /events/{id} – Détails
 * - PUT /events/{id} – Modifier
 * - DELETE /events/{id} – Supprimer
 * - GET /events?organizerId=x – Événements par utilisateur
 */
@RestController
@RequestMapping("/api/events")
@CrossOrigin(origins = "*")
public class EventController {

    private static final Logger logger = LoggerFactory.getLogger(EventController.class);

    @Autowired
    private EventService eventService;

    /**
     * POST /events – Créer un événement
     */
    @PostMapping
    public ResponseEntity<Event> createEvent(@Valid @RequestBody Event event) {
        logger.info("Requête de création d'événement reçue : {}", event.getTitle());
        
        try {
            Event createdEvent = eventService.createEvent(event);
            return new ResponseEntity<>(createdEvent, HttpStatus.CREATED);
        } catch (Exception e) {
            logger.error("Erreur lors de la création de l'événement", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * GET /events/{id} – Obtenir les détails d'un événement
     */
    @GetMapping("/{id}")
    public ResponseEntity<Event> getEventById(@PathVariable Long id) {
        logger.info("Requête de récupération d'événement reçue pour l'ID : {}", id);
        
        try {
            Event event = eventService.getEventById(id);
            return new ResponseEntity<>(event, HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Événement non trouvé avec l'ID : {}", id);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération de l'événement", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /events/{id} – Modifier un événement
     */
    @PutMapping("/{id}")
    public ResponseEntity<Event> updateEvent(@PathVariable Long id, @Valid @RequestBody Event eventDetails) {
        logger.info("Requête de mise à jour d'événement reçue pour l'ID : {}", id);
        
        try {
            Event updatedEvent = eventService.updateEvent(id, eventDetails);
            return new ResponseEntity<>(updatedEvent, HttpStatus.OK);
        } catch (RuntimeException e) {
            logger.error("Événement non trouvé avec l'ID : {}", id);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            logger.error("Erreur lors de la mise à jour de l'événement", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * DELETE /events/{id} – Supprimer un événement
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEvent(@PathVariable Long id) {
        logger.info("Requête de suppression d'événement reçue pour l'ID : {}", id);
        
        try {
            eventService.deleteEvent(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (RuntimeException e) {
            logger.error("Événement non trouvé avec l'ID : {}", id);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            logger.error("Erreur lors de la suppression de l'événement", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * GET /events – Obtenir tous les événements ou filtrer par organisateur
     */
    @GetMapping
    public ResponseEntity<List<Event>> getEvents(@RequestParam(required = false) Long organizerId,
                                                 @RequestParam(required = false) String title,
                                                 @RequestParam(required = false) String location,
                                                 @RequestParam(required = false, defaultValue = "false") boolean upcoming) {
        logger.info("Requête de récupération d'événements reçue - organizerId: {}, title: {}, location: {}, upcoming: {}", 
                   organizerId, title, location, upcoming);
        
        try {
            List<Event> events;
            
            if (organizerId != null) {
                if (upcoming) {
                    events = eventService.getUpcomingEventsByOrganizer(organizerId);
                } else {
                    events = eventService.getEventsByOrganizer(organizerId);
                }
            } else if (title != null) {
                events = eventService.searchEventsByTitle(title);
            } else if (location != null) {
                events = eventService.searchEventsByLocation(location);
            } else if (upcoming) {
                events = eventService.getUpcomingEvents();
            } else {
                events = eventService.getAllEvents();
            }
            
            return new ResponseEntity<>(events, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des événements", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * GET /events/upcoming – Obtenir tous les événements futurs
     */
    @GetMapping("/upcoming")
    public ResponseEntity<List<Event>> getUpcomingEvents() {
        logger.info("Requête de récupération des événements futurs reçue");
        
        try {
            List<Event> events = eventService.getUpcomingEvents();
            return new ResponseEntity<>(events, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des événements futurs", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * GET /events/organizer/{organizerId} – Obtenir les événements d'un organisateur
     */
    @GetMapping("/organizer/{organizerId}")
    public ResponseEntity<List<Event>> getEventsByOrganizer(@PathVariable Long organizerId) {
        logger.info("Requête de récupération des événements pour l'organisateur : {}", organizerId);
        
        try {
            List<Event> events = eventService.getEventsByOrganizer(organizerId);
            return new ResponseEntity<>(events, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("Erreur lors de la récupération des événements de l'organisateur", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
