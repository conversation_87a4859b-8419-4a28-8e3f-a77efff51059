<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="serverdiscover" />
        <module name="config-server" />
        <module name="ApiGateway" />
        <module name="microservices" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ApiGateway" options="-parameters" />
      <module name="Fournisseur_Back" options="-parameters" />
      <module name="config-server" options="-parameters" />
      <module name="microservices" options="-parameters" />
      <module name="serverdiscover" options="-parameters" />
    </option>
  </component>
</project>