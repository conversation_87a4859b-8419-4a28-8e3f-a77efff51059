# Nom de l'application
spring.application.name=USER

# Configuration de la base de données MySQL
spring.datasource.url=************************************************************************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Configuration de Hibernate (ORM)
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Configuration du serveur
server.port=8084

# Configuration Eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.hostname=localhost

# Désactiver le vérificateur de compatibilité Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Importation optionnelle du Config Server
spring.cloud.config.enabled=true
spring.config.import=optional:configserver:http://localhost:8888

# Autoriser l'écrasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafraîchir la config
management.endpoints.web.exposure.include=refresh

# Message de bienvenue
welcome.message=Welcome to our application!

# Spring Security OAuth2 Resource Server Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

# Keycloak Configuration
keycloak.auth-server-url=http://localhost:8080
keycloak.realm=RepasKeycloak
keycloak.resource=repas-service
keycloak.credentials.secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
keycloak.ssl-required=external
keycloak.use-resource-role-mappings=true
keycloak.bearer-only=true
keycloak.public-client=false

# JWT Configuration
jwt.auth.converter.resource-id=repas-service
jwt.auth.converter.principal-attribute=preferred_username
jwt.secret-key=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970

# Enable role conversion
keycloak.security-constraints[0].authRoles[0]=ADMIN
keycloak.security-constraints[0].securityCollections[0].patterns[0]=/api/users/*

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.oauth2=DEBUG
logging.level.org.keycloak=DEBUG
logging.level.root=INFO



# Google OAuth2 Configuration
google.clientId=407408718192.apps.googleusercontent.com
