-- Données de test pour l'application USER
-- Ce fichier sera exécuté automatiquement par Spring Boot

-- Insérer des utilisateurs de test avec des mots de passe encodés BCrypt
-- Mot de passe pour tous : "password123"
-- Hash BCrypt : $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.

INSERT INTO users (first_name, last_name, email, password, role, reset_token, token_expiration) VALUES 
('Admin', 'System', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'ADMIN', NULL, NULL),
('<PERSON>', 'Dupont', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'USER', NULL, NULL),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'USER', NULL, NULL),
('Pierre', 'Fournisseur', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'FOURNISSEUR', NULL, NULL),
('Sophie', 'Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'ADMIN', NULL, NULL),
('Test', 'User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'USER', NULL, NULL);
