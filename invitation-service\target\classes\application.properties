# Nom de l'application
spring.application.name=INVITATION

# Configuration de la base de données PostgreSQL
spring.datasource.url=*********************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Configuration de Hibernate (ORM)
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Configuration du serveur
server.port=8083

# Configuration Eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.hostname=localhost

# Désactiver le vérificateur de compatibilité Spring Cloud
spring.cloud.compatibility-verifier.enabled=false

# Importation optionnelle du Config Server
spring.cloud.config.enabled=true
spring.config.import=optional:configserver:http://localhost:8888

# Autoriser l'écrasement des beans
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=false

# Activer les endpoints pour rafraîchir la config
management.endpoints.web.exposure.include=refresh

# Configuration Kafka
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.properties.spring.json.add.type.headers=false

# Topics Kafka
kafka.topics.invitation-responded=invitation.responded

# Message de bienvenue
welcome.message=Welcome to Invitation Service!
