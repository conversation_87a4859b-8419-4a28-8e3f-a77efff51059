@echo off
echo ========================================
echo Test de connexion PostgreSQL
echo ========================================

echo 1. Test de connexion à PostgreSQL...
psql -U postgres -h localhost -d userdb -c "SELECT version();"

if %ERRORLEVEL% EQU 0 (
    echo ✅ Connexion PostgreSQL réussie !
) else (
    echo ❌ Erreur de connexion PostgreSQL
    echo Vérifiez que PostgreSQL est démarré et que les identifiants sont corrects
    pause
    exit /b 1
)

echo.
echo 2. Vérification de la base de données userdb...
psql -U postgres -h localhost -d userdb -c "\dt"

echo.
echo 3. Test de l'application Spring Boot...
echo Démarrage de l'application avec le profil PostgreSQL...
cd /d "%~dp0.."
mvn spring-boot:run -Dspring-boot.run.profiles=postgres

pause
