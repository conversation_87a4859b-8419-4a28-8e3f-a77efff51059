spring.application.name=api-gateway
server.port=8093

# Configuration Eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.hostname=localhost

# Configuration Keycloak
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/RepasKeycloak/protocol/openid-connect/certs

# Optional client configuration
spring.security.oauth2.client.provider.keycloak.issuer-uri=http://localhost:8080/realms/RepasKeycloak
spring.security.oauth2.client.provider.keycloak.user-name-attribute=preferred_username
spring.security.oauth2.client.registration.keycloak.client-id=repas-service
spring.security.oauth2.client.registration.keycloak.client-secret=xELXqoDJ4DRmBxdlQqDn6a9trwNh8Wjq
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email,roles

# Configuration des routes
spring.cloud.gateway.routes[0].id=user-service-auth
spring.cloud.gateway.routes[0].uri=lb://USER
spring.cloud.gateway.routes[0].predicates[0]=Path=/auth/**
spring.cloud.gateway.routes[0].filters[0]=RemoveRequestHeader=Cookie

spring.cloud.gateway.routes[1].id=user-service-api
spring.cloud.gateway.routes[1].uri=lb://USER
spring.cloud.gateway.routes[1].predicates[0]=Path=/api/**


# Mode reactif pour Spring Cloud Gateway
spring.main.web-application-type=reactive

# Configuration JWT
jwt.secret-key=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970

# Logging Configuration
logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.reactor.netty=DEBUG
logging.level.redisratelimiter=DEBUG
logging.level.org.springframework.web.cors=DEBUG
