-- Script de migration MySQL vers PostgreSQL
-- Exécuter ce script après avoir exporté les données de MySQL

-- 1. <PERSON><PERSON>er la table users en PostgreSQL
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255),
    role VARCHAR(255) NOT NULL,
    reset_token VARCHAR(255),
    token_expiration TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON> des index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_reset_token ON users(reset_token);

-- 3. Insérer des données de test (remplacer par vos vraies données)
INSERT INTO users (first_name, last_name, email, password, role) VALUES
('Admin', 'User', '<EMAIL>', '$2a$10$encrypted_password_here', 'ADMIN'),
('Test', 'User', '<EMAIL>', '$2a$10$encrypted_password_here', 'USER'),
('Supplier', 'Test', '<EMAIL>', '$2a$10$encrypted_password_here', 'FOURNISSEUR')
ON CONFLICT (email) DO NOTHING;

-- 4. Vérifier les données
SELECT * FROM users;
