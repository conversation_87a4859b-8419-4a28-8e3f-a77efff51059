spring.application.name=api-gateway
server.port=8093
spring.cloud.gateway.discovery.locator.enabled=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.register-with-eureka=true

# Routes
spring.cloud.gateway.routes[0].id=user-service-auth
spring.cloud.gateway.routes[0].uri=lb://USER
spring.cloud.gateway.routes[0].predicates[0]=Path=/auth/**

spring.cloud.gateway.routes[1].id=user-service-api
spring.cloud.gateway.routes[1].uri=lb://USER
spring.cloud.gateway.routes[1].predicates[0]=Path=/api/**

management.tracing.sampling.probability=1.0