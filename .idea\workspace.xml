<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="da455a24-aab2-4d8e-a5e4-2798089b9f64" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/USER/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/AuthenticationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/AuthenticationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/AuthenticationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/AuthenticationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/RegisterRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Auth/RegisterRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Entity/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Entity/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/ApplicationConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/ApplicationConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/KeycloakRoleConverter.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/KeycloakRoleConverter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/Security/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/controller/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/controller/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/repo/UserRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/repo/UserRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/JwtService.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/JwtService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/PDFService.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/PDFService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/java/com/example/user/service/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/USER/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/USER/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config-server/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/config-server/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config-server/src/main/resources/configurations/user.properties" beforeDir="false" afterPath="$PROJECT_DIR$/config-server/src/main/resources/configurations/user.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docker-compose.yml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dockerfile" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2t4kL93XBAffVxoDM3mbB6P96JA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.microservices [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.microservices [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.microservices [install].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;Database detector&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.ApiGatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.EventServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.FournisseurBackApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ProjetmicroservicesrepasfirasApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ServerdiscoverApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.config-server.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.usermicroservices.executor&quot;: &quot;Run&quot;,
    &quot;ToolWindow.Services.ShowToolbar&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/Stage GTI 2025/invitation-service&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.4275862&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.annotationProcessors&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Desktop\\projet microservicesUser Firas\\frontend-auth\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projet microservicesUser Firas\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\service" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices - Copie - Copie\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\Filter" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices - Copie - Copie\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\service" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras" />
      <recent name="C:\Users\<USER>\Desktop\Nouveau dossier\serverdiscover" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.projetmicroservicesrepasfiras.controller" />
      <recent name="com.example.microservice.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.usermicroservices">
    <configuration name="ApiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ApiGateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.apigateway.ApiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EventServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="event-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.eventservice.EventServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InvitationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="invitation-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.invitationservice.InvitationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerdiscoverApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="serverdiscover" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.serverdiscover.ServerdiscoverApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="config-server" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="config-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.spring.config_server.ConfigServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="usermicroservices" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="microservices" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.user.ProjetmicroservicesrepasfirasApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.ApiGatewayApplication" />
      <item itemvalue="Spring Boot.EventServiceApplication" />
      <item itemvalue="Spring Boot.InvitationServiceApplication" />
      <item itemvalue="Spring Boot.ServerdiscoverApplication" />
      <item itemvalue="Spring Boot.config-server" />
      <item itemvalue="Spring Boot.usermicroservices" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.189" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.189" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="da455a24-aab2-4d8e-a5e4-2798089b9f64" name="Changes" comment="" />
      <created>1739618675646</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1739618675646</updated>
      <workItem from="1739618676865" duration="6265000" />
      <workItem from="1739626980815" duration="1928000" />
      <workItem from="1739645988780" duration="5962000" />
      <workItem from="1739781596820" duration="2957000" />
      <workItem from="1740660439101" duration="4577000" />
      <workItem from="1740690751056" duration="8000" />
      <workItem from="1740989297707" duration="4856000" />
      <workItem from="1741001780073" duration="1010000" />
      <workItem from="1743085015008" duration="21833000" />
      <workItem from="1743186671788" duration="278000" />
      <workItem from="1743187003498" duration="8017000" />
      <workItem from="1743251035626" duration="10057000" />
      <workItem from="1743430255272" duration="897000" />
      <workItem from="1743433812012" duration="16991000" />
      <workItem from="1743511418884" duration="22787000" />
      <workItem from="1743609771407" duration="697000" />
      <workItem from="1743612229352" duration="966000" />
      <workItem from="1743779369099" duration="14287000" />
      <workItem from="1743891220122" duration="4837000" />
      <workItem from="1743949817526" duration="4810000" />
      <workItem from="1743958042330" duration="8250000" />
      <workItem from="1743970544305" duration="6034000" />
      <workItem from="1744013414509" duration="5889000" />
      <workItem from="1744313892833" duration="3794000" />
      <workItem from="1744319467090" duration="2012000" />
      <workItem from="1744360673847" duration="7000" />
      <workItem from="1744371603762" duration="13274000" />
      <workItem from="1744387492021" duration="6645000" />
      <workItem from="1744405721253" duration="2682000" />
      <workItem from="1744453426765" duration="13316000" />
      <workItem from="1744470247277" duration="19041000" />
      <workItem from="1744795549275" duration="2158000" />
      <workItem from="1745091487128" duration="4848000" />
      <workItem from="1745145424713" duration="8811000" />
      <workItem from="1745158839363" duration="65000" />
      <workItem from="1745158942482" duration="174000" />
      <workItem from="1745171390684" duration="1218000" />
      <workItem from="1749021857528" duration="1404000" />
      <workItem from="1749031889022" duration="1300000" />
      <workItem from="1749554154258" duration="4374000" />
      <workItem from="1749628319385" duration="9035000" />
      <workItem from="1749641909286" duration="2000" />
      <workItem from="1749641980572" duration="1020000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".gitattributes" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="io.jsonwebtoken.security.SignatureException" package="io.jsonwebtoken.security" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>