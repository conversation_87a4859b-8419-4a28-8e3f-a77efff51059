<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="da455a24-aab2-4d8e-a5e4-2798089b9f64" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docker-compose.yml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dockerfile" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2t4kL93XBAffVxoDM3mbB6P96JA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.microservices [clean].executor": "Run",
    "Maven.microservices [compile].executor": "Run",
    "Maven.microservices [install].executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "Database detector",
    "Notification.DoNotAsk-Database detector": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.ApiGatewayApplication.executor": "Run",
    "Spring Boot.FournisseurBackApplication.executor": "Run",
    "Spring Boot.ProjetmicroservicesrepasfirasApplication.executor": "Run",
    "Spring Boot.ServerdiscoverApplication.executor": "Run",
    "Spring Boot.config-server.executor": "Run",
    "Spring Boot.usermicroservices.executor": "Run",
    "ToolWindow.Services.ShowToolbar": "false",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/Stage GTI 2025/USER",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4275862",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.annotationProcessors",
    "ts.external.directory.path": "C:\\Users\\<USER>\\Desktop\\projet microservicesUser Firas\\frontend-auth\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projet microservicesUser Firas\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\service" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices - Copie - Copie\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\Filter" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices - Copie - Copie\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras\service" />
      <recent name="C:\Users\<USER>\Desktop\projet microservices\projetmicroservicesrepasfiras\src\main\java\com\example\projetmicroservicesrepasfiras" />
      <recent name="C:\Users\<USER>\Desktop\Nouveau dossier\serverdiscover" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.projetmicroservicesrepasfiras.controller" />
      <recent name="com.example.microservice.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.usermicroservices">
    <configuration name="ApiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ApiGateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.apigateway.ApiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerdiscoverApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="serverdiscover" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.serverdiscover.ServerdiscoverApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="config-server" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="config-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.spring.config_server.ConfigServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="usermicroservices" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="microservices" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.user.ProjetmicroservicesrepasfirasApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.ApiGatewayApplication" />
      <item itemvalue="Spring Boot.usermicroservices" />
      <item itemvalue="Spring Boot.ServerdiscoverApplication" />
      <item itemvalue="Spring Boot.config-server" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.189" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.189" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="da455a24-aab2-4d8e-a5e4-2798089b9f64" name="Changes" comment="" />
      <created>1739618675646</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1739618675646</updated>
      <workItem from="1739618676865" duration="6265000" />
      <workItem from="1739626980815" duration="1928000" />
      <workItem from="1739645988780" duration="5962000" />
      <workItem from="1739781596820" duration="2957000" />
      <workItem from="1740660439101" duration="4577000" />
      <workItem from="1740690751056" duration="8000" />
      <workItem from="1740989297707" duration="4856000" />
      <workItem from="1741001780073" duration="1010000" />
      <workItem from="1743085015008" duration="21833000" />
      <workItem from="1743186671788" duration="278000" />
      <workItem from="1743187003498" duration="8017000" />
      <workItem from="1743251035626" duration="10057000" />
      <workItem from="1743430255272" duration="897000" />
      <workItem from="1743433812012" duration="16991000" />
      <workItem from="1743511418884" duration="22787000" />
      <workItem from="1743609771407" duration="697000" />
      <workItem from="1743612229352" duration="966000" />
      <workItem from="1743779369099" duration="14287000" />
      <workItem from="1743891220122" duration="4837000" />
      <workItem from="1743949817526" duration="4810000" />
      <workItem from="1743958042330" duration="8250000" />
      <workItem from="1743970544305" duration="6034000" />
      <workItem from="1744013414509" duration="5889000" />
      <workItem from="1744313892833" duration="3794000" />
      <workItem from="1744319467090" duration="2012000" />
      <workItem from="1744360673847" duration="7000" />
      <workItem from="1744371603762" duration="13274000" />
      <workItem from="1744387492021" duration="6645000" />
      <workItem from="1744405721253" duration="2682000" />
      <workItem from="1744453426765" duration="13316000" />
      <workItem from="1744470247277" duration="19041000" />
      <workItem from="1744795549275" duration="2158000" />
      <workItem from="1745091487128" duration="4848000" />
      <workItem from="1745145424713" duration="8811000" />
      <workItem from="1745158839363" duration="65000" />
      <workItem from="1745158942482" duration="174000" />
      <workItem from="1745171390684" duration="1218000" />
      <workItem from="1749021857528" duration="1404000" />
      <workItem from="1749031889022" duration="1300000" />
      <workItem from="1749554154258" duration="4374000" />
      <workItem from="1749628319385" duration="595000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".gitattributes" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="io.jsonwebtoken.security.SignatureException" package="io.jsonwebtoken.security" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>