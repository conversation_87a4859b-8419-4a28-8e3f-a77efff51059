spring.application.name=USER

spring.datasource.url=****************************************************************************************************
spring.datasource.username=user
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.hibernate.show-sql=true

# Configuration Eureka
eureka.instance.hostname=localhost
eureka.client.fetch-registry=true
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.client.register-with-eureka=true

# Port du service
server.port=8084

# Message de bienvenue
welcome.message=Bienvenue dans mon application Microservices!
